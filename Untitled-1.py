#!/usr/bin/env python3
"""
run_model.py

Multi-task mutation-count prediction from CA tracks at 1MB/100KB/10KB.
Aligned on 10KB grid. Loss = mse | poisson | nb (default nb).

Examples:
  PROJ=/.mounts/labs/reimandlab/private/users/hanli/ca-rt-ml
  python scripts/run_model.py \
    --ca_1mb   $PROJ/data/CA_RT_data/CA_RT_1MB.tsv \
    --ca_100kb $PROJ/data/CA_RT_data/CA_RT_100KB.tsv \
    --ca_10kb  $PROJ/data/CA_RT_data/CA_RT_10KB.tsv \
    --mut_1mb   $PROJ/data/CA_RT_data/HMF_mutation_1MB.csv \
    --mut_100kb $PROJ/data/CA_RT_data/HMF_mutation_100KB.csv \
    --mut_10kb  $PROJ/data/CA_RT_data/HMF_mutation_10KB.csv \
    --ctype breast \
    --loss nb --epochs 80 --batch_size 256 \
    --zscore --feature_clip 8.0 \
    --drop_zero_var_thresh 1e-6 \
    --theta_l2 1e-4 \
    --outdir $PROJ/results_mt/breast
"""
from __future__ import annotations
import argparse, math, csv
from pathlib import Path
from typing import List, Tuple

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, random_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

# -----------------------------------------------------------------------------
# I/O & preprocessing
# -----------------------------------------------------------------------------

def _canon_cols(df: pd.DataFrame) -> pd.DataFrame:
    df = df.copy()
    df.columns = [c.lower().strip().replace(" ", "_") for c in df.columns]
    return df

def _read_ca(path: Path) -> pd.DataFrame:
    return pd.read_csv(path, sep="\t", compression="infer", low_memory=False)

def _read_mut(path: Path) -> pd.DataFrame:
    return pd.read_csv(path, compression="infer", low_memory=False)

def _select_ct(df_mut: pd.DataFrame, ctype: str) -> pd.DataFrame:
    c = ctype.lower()
    if c not in df_mut.columns:
        raise KeyError(f"Cancer type '{ctype}' not in mutation table.")
    return df_mut[["chr","start", c]].rename(columns={c: "y"})

def _zscore_inplace(A: np.ndarray) -> None:
    m = np.nanmean(A, axis=0, keepdims=True)
    s = np.nanstd(A, axis=0, keepdims=True)
    s = np.where(s < 1e-12, 1.0, s)
    A -= m
    A /= s

def _sanitize_inplace(A: np.ndarray, fill: float = 0.0, clip_abs: float|None = None) -> None:
    np.nan_to_num(A, copy=False, nan=fill, posinf=fill, neginf=fill)
    if clip_abs is not None:
        np.clip(A, -clip_abs, clip_abs, out=A)

def prepare_arrays(
    ca_1mb: Path, ca_100kb: Path, ca_10kb: Path,
    mut_1mb: Path, mut_100kb: Path, mut_10kb: Path,
    ctype: str,
    zscore: bool = False,
    feature_clip: float | None = None,
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, list, list, list]:
    """Return (X1MB, X100KB, X10KB, Y1MB, Y100KB, Y10KB, cols1, cols2, cols3) aligned on 10KB bins."""
    ca1 = _canon_cols(_read_ca(ca_1mb))
    ca2 = _canon_cols(_read_ca(ca_100kb))
    ca3 = _canon_cols(_read_ca(ca_10kb))
    m1  = _canon_cols(_read_mut(mut_1mb))
    m2  = _canon_cols(_read_mut(mut_100kb))
    m3  = _canon_cols(_read_mut(mut_10kb))

    for df,name in [(ca1,'CA_1MB'),(ca2,'CA_100KB'),(ca3,'CA_10KB'),
                    (m1,'MUT_1MB'),(m2,'MUT_100KB'),(m3,'MUT_10KB')]:
        if not set(['chr','start']).issubset(df.columns):
            raise KeyError(f"{name} must contain 'chr' and 'start' columns")

    base = ca3[['chr','start']].copy()
    base['start'] = pd.to_numeric(base['start'], errors='coerce').fillna(0).astype('int64')

    # mutation CSVs use 1-based bin starts; align from 10kb
    base['start_100kb'] = ((base['start'] - 1) // 100_000) * 100_000 + 1
    base['start_1mb']   = ((base['start'] - 1) // 1_000_000) * 1_000_000 + 1

    def feat_cols(df): return [c for c in df.columns if c not in ('chr','start')]

    X1_cols = feat_cols(ca1)
    base = base.merge(ca1.rename(columns={'start':'start_1mb'}), on=['chr','start_1mb'], how='left')
    X2_cols = feat_cols(ca2)
    base = base.merge(ca2.rename(columns={'start':'start_100kb'}), on=['chr','start_100kb'], how='left')
    X3_cols = feat_cols(ca3)
    base = base.merge(ca3, on=['chr','start'], how='left')

    y1 = _select_ct(m1, ctype).rename(columns={'y':'y_1mb'})
    y2 = _select_ct(m2, ctype).rename(columns={'y':'y_100kb'})
    y3 = _select_ct(m3, ctype).rename(columns={'y':'y_10kb'})

    base = base.merge(y1.rename(columns={'start':'start_1mb'}), on=['chr','start_1mb'], how='left')
    base = base.merge(y2.rename(columns={'start':'start_100kb'}), on=['chr','start_100kb'], how='left')
    base = base.merge(y3, on=['chr','start'], how='left')

    before = len(base)
    base = base.dropna(subset=['y_1mb','y_100kb','y_10kb']).reset_index(drop=True)
    after = len(base)
    print(f"Filtered rows without targets: kept {after}/{before}")

    X1 = base[X1_cols].to_numpy(dtype=np.float32, copy=False)
    X2 = base[X2_cols].to_numpy(dtype=np.float32, copy=False)
    X3 = base[X3_cols].to_numpy(dtype=np.float32, copy=False)
    Y1 = base['y_1mb'].to_numpy(dtype=np.float32, copy=False)
    Y2 = base['y_100kb'].to_numpy(dtype=np.float32, copy=False)
    Y3 = base['y_10kb'].to_numpy(dtype=np.float32, copy=False)

    _sanitize_inplace(X1, fill=0.0); _sanitize_inplace(X2, fill=0.0); _sanitize_inplace(X3, fill=0.0)
    if zscore:
        _zscore_inplace(X1); _zscore_inplace(X2); _zscore_inplace(X3)
    if feature_clip is not None:
        _sanitize_inplace(X1, clip_abs=feature_clip); _sanitize_inplace(X2, clip_abs=feature_clip); _sanitize_inplace(X3, clip_abs=feature_clip)

    Y1 = np.clip(np.nan_to_num(Y1, nan=0.0, posinf=0.0), 0, None)
    Y2 = np.clip(np.nan_to_num(Y2, nan=0.0, posinf=0.0), 0, None)
    Y3 = np.clip(np.nan_to_num(Y3, nan=0.0, posinf=0.0), 0, None)

    return X1, X2, X3, Y1, Y2, Y3, X1_cols, X2_cols, X3_cols

# -----------------------------------------------------------------------------
# Dataset
# -----------------------------------------------------------------------------
class MTDataset(Dataset):
    def __init__(self, a1,a2,a3,y1,y2,y3):
        n = len(y3)
        for arr in [a1,a2,a3,y1,y2]:
            assert len(arr)==n
        self.x1 = torch.from_numpy(a1).float()
        self.x2 = torch.from_numpy(a2).float()
        self.x3 = torch.from_numpy(a3).float()
        self.y1 = torch.from_numpy(y1).float()
        self.y2 = torch.from_numpy(y2).float()
        self.y3 = torch.from_numpy(y3).float()
    def __len__(self): return len(self.y3)
    def __getitem__(self, i): return self.x1[i],self.x2[i],self.x3[i],self.y1[i],self.y2[i],self.y3[i]

# -----------------------------------------------------------------------------
# Model (MLP branches)
# -----------------------------------------------------------------------------
class MultiTaskMLP(nn.Module):
    """Three MLP branches -> shared -> three mu heads (Softplus)."""
    def __init__(self, f1:int, f2:int, f3:int, hidden:int=256, shared:int=256,
                 loss_type:str='nb', dropout: float = 0.0,
                 mu_caps: tuple[float,float,float] = (1e6,1e6,1e6),
                 fix_theta: float | None = None):
        super().__init__()
        self.loss_type = loss_type
        self.mu_caps = mu_caps
        self.fix_theta = fix_theta

        def br(fin:int):
            return nn.Sequential(
                nn.Linear(fin, hidden), nn.GELU(),
                nn.Dropout(dropout),
                nn.Linear(hidden, hidden), nn.GELU(),
            )
        self.b1, self.b2, self.b3 = br(f1), br(f2), br(f3)
        self.shared = nn.Sequential(nn.Linear(hidden*3, shared), nn.GELU(), nn.Dropout(dropout))

        self.mu1 = nn.Sequential(nn.Linear(shared,1), nn.Softplus())
        self.mu2 = nn.Sequential(nn.Linear(shared,1), nn.Softplus())
        self.mu3 = nn.Sequential(nn.Linear(shared,1), nn.Softplus())

        if fix_theta is None:
            self.theta1 = nn.Parameter(torch.tensor([10.0]))
            self.theta2 = nn.Parameter(torch.tensor([10.0]))
            self.theta3 = nn.Parameter(torch.tensor([10.0]))
        else:
            # register as buffers so they move to device but aren't learnable
            self.register_buffer('theta1', torch.tensor([fix_theta], dtype=torch.float32))
            self.register_buffer('theta2', torch.tensor([fix_theta], dtype=torch.float32))
            self.register_buffer('theta3', torch.tensor([fix_theta], dtype=torch.float32))

    def forward(self, x1,x2,x3):
        h = torch.cat([self.b1(x1), self.b2(x2), self.b3(x3)], dim=1)
        h = self.shared(h)
        mu1 = torch.clamp(self.mu1(h).squeeze(-1), 1e-6, self.mu_caps[0])
        mu2 = torch.clamp(self.mu2(h).squeeze(-1), 1e-6, self.mu_caps[1])
        mu3 = torch.clamp(self.mu3(h).squeeze(-1), 1e-6, self.mu_caps[2])
        if self.fix_theta is None:
            t1 = torch.clamp(F.softplus(self.theta1), 1e-6, 1e6)
            t2 = torch.clamp(F.softplus(self.theta2), 1e-6, 1e6)
            t3 = torch.clamp(F.softplus(self.theta3), 1e-6, 1e6)
        else:
            t1 = self.theta1
            t2 = self.theta2
            t3 = self.theta3
        return (mu1,mu2,mu3), (t1,t2,t3)

# -----------------------------------------------------------------------------
# Losses & loops
# -----------------------------------------------------------------------------

def poisson_nll(mu, y):
    return nn.PoissonNLLLoss(log_input=False, full=True, reduction='mean')(mu, y)

def nb_nll_stable(y, mu, theta, eps: float = 1e-8):
    # Gamma-Poisson (mean=mu, shape=theta) log-likelihood, numerically stable
    y = torch.clamp(y, 0, 1e12)
    theta = torch.clamp(theta, 1e-6, 1e12)
    log_theta = torch.log(theta + eps)
    log_mu = torch.log(mu + eps)
    log_theta_mu = log_theta + torch.log1p(mu / (theta + eps))
    ll = (torch.lgamma(y + theta) - torch.lgamma(theta) - torch.lgamma(y + 1.0)
          + theta * (log_theta - log_theta_mu) + y * (log_mu - log_theta_mu))
    return -ll.mean()

def step_loss(loss_type, mus, thetas, ys, weights, theta_l2: float = 0.0):
    (mu1,mu2,mu3) = mus; (t1,t2,t3) = thetas; (y1,y2,y3) = ys
    if loss_type=='mse':
        l1,l2,l3 = F.mse_loss(mu1,y1), F.mse_loss(mu2,y2), F.mse_loss(mu3,y3)
    elif loss_type=='poisson':
        l1,l2,l3 = poisson_nll(mu1,y1), poisson_nll(mu2,y2), poisson_nll(mu3,y3)
    else:  # nb
        l1,l2,l3 = nb_nll_stable(y1,mu1,t1), nb_nll_stable(y2,mu2,t2), nb_nll_stable(y3,mu3,t3)
    total = weights[0]*l1 + weights[1]*l2 + weights[2]*l3
    if theta_l2 > 0.0:
        # penalize very large/small theta via log-theta^2
        def pen(t): 
            lt = torch.log(torch.clamp(t,1e-8,1e12))
            return (lt*lt).mean()
        total = total + theta_l2 * (pen(t1)+pen(t2)+pen(t3))
    return total, (l1,l2,l3)

def train_epoch(model, loader, opt, loss_type, weights, device, max_grad_norm: float = 5.0, theta_l2: float = 0.0):
    model.train(); run=0.0
    for x1,x2,x3,y1,y2,y3 in loader:
        x1,x2,x3 = x1.to(device),x2.to(device),x3.to(device)
        y1,y2,y3 = y1.to(device),y2.to(device),y3.to(device)
        opt.zero_grad()
        mus,thetas = model(x1,x2,x3)
        total,(l1,l2,l3) = step_loss(loss_type,mus,thetas,(y1,y2,y3),weights,theta_l2=theta_l2)
        total.backward()
        if max_grad_norm is not None:
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
        opt.step(); run += total.item()*len(y1)
    return run/len(loader.dataset)

@torch.no_grad()
def evaluate(model, loader, device):
    model.eval()
    preds=[[],[],[]]; ys=[[],[],[]]
    thetas_seen=[]
    for x1,x2,x3,y1,y2,y3 in loader:
        x1,x2,x3 = x1.to(device),x2.to(device),x3.to(device)
        (mu1,mu2,mu3),ths = model(x1,x2,x3)
        mu1 = torch.nan_to_num(mu1, nan=0.0, posinf=1e12, neginf=0.0)
        mu2 = torch.nan_to_num(mu2, nan=0.0, posinf=1e12, neginf=0.0)
        mu3 = torch.nan_to_num(mu3, nan=0.0, posinf=1e12, neginf=0.0)
        preds[0].append(mu1.cpu()); preds[1].append(mu2.cpu()); preds[2].append(mu3.cpu())
        ys[0].append(y1); ys[1].append(y2); ys[2].append(y3)
        thetas_seen.append([t.item() for t in ths])
    mae=[]; mse=[]; r2=[]; pm=[]; ps=[]
    for i in range(3):
        p = torch.cat(preds[i]).numpy().astype(np.float64)
        y = torch.cat(ys[i]).numpy().astype(np.float64)
        mae.append(mean_absolute_error(y,p))
        mse.append(mean_squared_error(y,p))
        r2.append(r2_score(y,p))
        pm.append(float(np.mean(p))); ps.append(float(np.std(p)))
    # report average theta over the loop
    th_avg = np.mean(np.array(thetas_seen), axis=0) if thetas_seen else np.array([np.nan,np.nan,np.nan])
    return mae, mse, r2, pm, ps, th_avg

# Stable inverse softplus: for y<20 use log(expm1), else ~ y
def inv_softplus_stable(y: float, eps: float = 1e-8) -> float:
    y = max(y, eps)
    if y < 20.0:
        return float(np.log(np.expm1(y)))
    return float(y + np.log1p(-np.exp(-y)))

def init_mu_biases(model: "MultiTaskMLP", y_means: tuple[float,float,float], mu_caps: tuple[float,float,float]):
    with torch.no_grad():
        for head, ym, cap in zip([model.mu1, model.mu2, model.mu3], y_means, mu_caps):
            target = float(min(ym, 0.8*cap))
            pre = inv_softplus_stable(target)
            lin: nn.Linear = head[0]
            lin.bias.data.fill_(pre)

# -----------------------------------------------------------------------------
# Main
# -----------------------------------------------------------------------------

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--ca_1mb', required=True)
    ap.add_argument('--ca_100kb', required=True)
    ap.add_argument('--ca_10kb', required=True)
    ap.add_argument('--mut_1mb', required=True)
    ap.add_argument('--mut_100kb', required=True)
    ap.add_argument('--mut_10kb', required=True)
    ap.add_argument('--ctype', required=True)
    ap.add_argument('--loss', choices=['mse','poisson','nb'], default='nb')
    ap.add_argument('--w1mb', type=float, default=0.3)
    ap.add_argument('--w100kb', type=float, default=0.3)
    ap.add_argument('--w10kb', type=float, default=1.0)
    ap.add_argument('--epochs', type=int, default=50)
    ap.add_argument('--batch_size', type=int, default=256)
    ap.add_argument('--lr', type=float, default=1e-3)
    ap.add_argument('--patience', type=int, default=10)
    ap.add_argument('--seed', type=int, default=42)
    ap.add_argument('--zscore', action='store_true', help='z-score CA features per scale')
    ap.add_argument('--feature_clip', type=float, default=None, help='abs clip after zscore, e.g., 8.0')
    ap.add_argument('--drop_zero_var_thresh', type=float, default=None, help='drop features with train-std<thresh')
    ap.add_argument('--dropout', type=float, default=0.0)
    ap.add_argument('--theta_l2', type=float, default=0.0, help='L2 on log-theta (NB only)')
    ap.add_argument('--fix_theta', type=float, default=None, help='fix NB theta to this value (e.g., 50).')
    ap.add_argument('--outdir', required=True)
    args = ap.parse_args()

    torch.manual_seed(args.seed); np.random.seed(args.seed)
    torch.backends.cuda.matmul.allow_tf32 = True
    torch.set_float32_matmul_precision('medium')

    outdir = Path(args.outdir); outdir.mkdir(parents=True, exist_ok=True)

    print(f"========== CANCER TYPE: {args.ctype} ==========")
    X1,X2,X3,Y1,Y2,Y3, C1, C2, C3 = prepare_arrays(
        Path(args.ca_1mb), Path(args.ca_100kb), Path(args.ca_10kb),
        Path(args.mut_1mb), Path(args.mut_100kb), Path(args.mut_10kb),
        ctype=args.ctype, zscore=args.zscore, feature_clip=args.feature_clip
    )
    print(f"Prepared arrays [{args.ctype}]: X1MB={X1.shape} X100KB={X2.shape} X10KB={X3.shape}  Y10KB={Y3.shape}")

    # Dataset and split
    ds_full = MTDataset(X1,X2,X3,Y1,Y2,Y3)
    n_train = int(0.8*len(ds_full)); n_val = len(ds_full)-n_train
    train_ds, val_ds = random_split(ds_full, [n_train,n_val], generator=torch.Generator().manual_seed(args.seed))

    # -------- Feature variance diagnostics on TRAIN only --------
    X1_tr = train_ds[:][0].numpy(); X2_tr = train_ds[:][1].numpy(); X3_tr = train_ds[:][2].numpy()
    def var_report(X, name, cols):
        s = X.std(axis=0)
        frac_zero = float(np.mean(s < 1e-8))
        print(f"{name} features: {X.shape[1]}  zero-std frac (train): {frac_zero:.4f}")
        return s
    s1 = var_report(X1_tr, "1MB", C1)
    s2 = var_report(X2_tr, "100KB", C2)
    s3 = var_report(X3_tr, "10KB", C3)

    if args.drop_zero_var_thresh is not None:
        def drop_low_var(X_tr, X_all, s, thresh, name):
            keep = s >= thresh
            print(f"Dropping {np.sum(~keep)} {name} features with std<{thresh}")
            return X_all[:, keep], keep
        X1, k1 = drop_low_var(X1_tr, X1, s1, args.drop_zero_var_thresh, "1MB")
        X2, k2 = drop_low_var(X2_tr, X2, s2, args.drop_zero_var_thresh, "100KB")
        X3, k3 = drop_low_var(X3_tr, X3, s3, args.drop_zero_var_thresh, "10KB")
        # rebuild datasets after dropping
        ds_full = MTDataset(X1,X2,X3,Y1,Y2,Y3)
        n_train = int(0.8*len(ds_full)); n_val = len(ds_full)-n_train
        train_ds, val_ds = random_split(ds_full, [n_train,n_val], generator=torch.Generator().manual_seed(args.seed))
        X1_tr = train_ds[:][0].numpy(); X2_tr = train_ds[:][1].numpy(); X3_tr = train_ds[:][2].numpy()

    train_loader = DataLoader(train_ds, batch_size=args.batch_size, shuffle=True, drop_last=True, pin_memory=True)
    val_loader   = DataLoader(val_ds,   batch_size=args.batch_size, shuffle=False, pin_memory=True)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print('Device:', device)

    # Baselines on validation set (zero and mean)
    with torch.no_grad():
        y1v = torch.cat([b[3] for b in val_loader]).numpy().astype(np.float64)
        y2v = torch.cat([b[4] for b in val_loader]).numpy().astype(np.float64)
        y3v = torch.cat([b[5] for b in val_loader]).numpy().astype(np.float64)
        for name,y in zip(['1MB','100KB','10KB'], [y1v,y2v,y3v]):
            mse_zero = mean_squared_error(y, np.zeros_like(y))
            mse_mean = mean_squared_error(y, np.full_like(y, y.mean()))
            print(f"Val baseline {name}: MSE_zero={mse_zero:.4f}  MSE_mean={mse_mean:.4f}")

    # Data-driven caps (per head) from TRAIN split
    Y1_tr = train_ds[:][3].numpy(); Y2_tr = train_ds[:][4].numpy(); Y3_tr = train_ds[:][5].numpy()
    cap1 = float(max(10.0, np.percentile(Y1_tr, 99.9)*2.0))
    cap2 = float(max(10.0, np.percentile(Y2_tr, 99.9)*2.0))
    cap3 = float(max(10.0, np.percentile(Y3_tr, 99.9)*2.0))
    mu_caps = (cap1, cap2, cap3)
    print(f"Caps (train p99.9*2): 1MB={cap1:.3f}  100KB={cap2:.3f}  10KB={cap3:.3f}")

    model = MultiTaskMLP(
        X1.shape[1], X2.shape[1], X3.shape[1],
        hidden=256, shared=256, loss_type=args.loss, dropout=args.dropout,
        mu_caps=mu_caps, fix_theta=args.fix_theta
    ).to(device)

    # Init mu-head biases near global means but under the cap
    y_means = (float(Y1_tr.mean()), float(Y2_tr.mean()), float(Y3_tr.mean()))
    print(f"Init mu to means (train): 1MB={y_means[0]:.3f}  100KB={y_means[1]:.3f}  10KB={y_means[2]:.3f}")
    init_mu_biases(model, y_means, mu_caps)

    opt = torch.optim.AdamW(model.parameters(), lr=args.lr)
    weights = (args.w1mb, args.w100kb, args.w10kb)

    # ---------------- Best-epoch tracking by highest R2 at 10KB ----------------
    best_r2_10 = -float('inf')
    best_epoch = 0
    no_imp = 0
    log_rows: List[List] = []

    for epoch in range(1, args.epochs+1):
        tr_loss = train_epoch(model, train_loader, opt, args.loss, weights, device,
                              max_grad_norm=5.0, theta_l2=args.theta_l2)
        val_mae, val_mse, val_r2, pm, ps, th_avg = evaluate(model, val_loader, device)
        print(f"Epoch {epoch}/{args.epochs}  trainLoss={tr_loss:.4f}  "
              f"valMAE[1MB|100KB|10KB]={val_mae[0]:.4f}|{val_mae[1]:.4f}|{val_mae[2]:.4f}  "
              f"valMSE[1MB|100KB|10KB]={val_mse[0]:.4f}|{val_mse[1]:.4f}|{val_mse[2]:.4f}  "
              f"valR2[1MB|100KB|10KB]={val_r2[0]:.4f}|{val_r2[1]:.4f}|{val_r2[2]:.4f}")
        print(f"Pred mean/std: 1MB={pm[0]:.3f}/{ps[0]:.3f}  100KB={pm[1]:.3f}/{ps[1]:.3f}  10KB={pm[2]:.3f}/{ps[2]:.3f}  "
              f"Theta(avg): {th_avg[0]:.3f}|{th_avg[1]:.3f}|{th_avg[2]:.3f}")

        # Log row
        log_rows.append([epoch, tr_loss, *val_mae, *val_mse, *val_r2, *pm, *ps, *th_avg])

        # Monitor: maximize R2 at 10KB
        if val_r2[2] > best_r2_10:
            best_r2_10 = val_r2[2]
            best_epoch = epoch
            no_imp = 0
            torch.save(model.state_dict(), outdir/'best_model.pt')
        else:
            no_imp += 1
            if no_imp >= args.patience:
                print('Early stopping after', epoch, 'epochs (no improvement on R2_10KB)')
                break

    # ---------------- Reload best and print Best Epoch Summary ----------------
    model.load_state_dict(torch.load(outdir/'best_model.pt', map_location=device))
    val_mae, val_mse, val_r2, pm, ps, th_avg = evaluate(model, val_loader, device)

    print("\n================ Best Epoch Summary ================")
    print(f"Best epoch: {best_epoch}  (monitor = R2_10KB)")
    print(f"Best valMAE [1MB|100KB|10KB] = {val_mae[0]:.4f} | {val_mae[1]:.4f} | {val_mae[2]:.4f}")
    print(f"Best valMSE [1MB|100KB|10KB] = {val_mse[0]:.4f} | {val_mse[1]:.4f} | {val_mse[2]:.4f}")
    print(f"Best valR2  [1MB|100KB|10KB] = {val_r2[0]:.4f} | {val_r2[1]:.4f} | {val_r2[2]:.4f}")
    print(f"Best pred mean/std [1MB] {pm[0]:.3f}/{ps[0]:.3f}  "
          f"[100KB] {pm[1]:.3f}/{ps[1]:.3f}  [10KB] {pm[2]:.3f}/{ps[2]:.3f}")
    print(f"Best theta avg [1MB|100KB|10KB] = {th_avg[0]:.3f} | {th_avg[1]:.3f} | {th_avg[2]:.3f}")
    print("====================================================\n")

    # Final metrics & log (write best-run numbers)
    with open(outdir/'metrics.txt','w') as fh:
        fh.write(f"ctype\t{args.ctype}\nloss\t{args.loss}\n")
        fh.write(f"best_epoch\t{best_epoch}\nmonitor\tR2_10KB\nbest_monitor_value\t{val_r2[2]}\n")
        fh.write(f"val_MAE_1MB\t{val_mae[0]}\nval_MAE_100KB\t{val_mae[1]}\nval_MAE_10KB\t{val_mae[2]}\n")
        fh.write(f"val_MSE_1MB\t{val_mse[0]}\nval_MSE_100KB\t{val_mse[1]}\nval_MSE_10KB\t{val_mse[2]}\n")
        fh.write(f"val_R2_1MB\t{val_r2[0]}\nval_R2_100KB\t{val_r2[1]}\nval_R2_10KB\t{val_r2[2]}\n")
        fh.write(f"pred_mean_1MB\t{pm[0]}\npred_std_1MB\t{ps[0]}\n")
        fh.write(f"pred_mean_100KB\t{pm[1]}\npred_std_100KB\t{ps[1]}\n")
        fh.write(f"pred_mean_10KB\t{pm[2]}\npred_std_10KB\t{ps[2]}\n")
        fh.write(f"theta_avg_1MB\t{th_avg[0]}\ntheta_avg_100KB\t{th_avg[1]}\ntheta_avg_10KB\t{th_avg[2]}\n")

    with open(outdir/'train_log.tsv','w',newline='') as fh:
        wr = csv.writer(fh, delimiter='\t')
        wr.writerow([
            'epoch','trainLoss',
            'valMAE_1MB','valMAE_100KB','valMAE_10KB',
            'valMSE_1MB','valMSE_100KB','valMSE_10KB',
            'valR2_1MB','valR2_100KB','valR2_10KB',
            'predMean_1MB','predMean_100KB','predMean_10KB',
            'predStd_1MB','predStd_100KB','predStd_10KB',
            'thetaAvg_1MB','thetaAvg_100KB','thetaAvg_10KB'
        ])
        wr.writerows(log_rows)

    print('Finished. Best model and metrics written to', outdir)


if __name__ == '__main__':
    main()